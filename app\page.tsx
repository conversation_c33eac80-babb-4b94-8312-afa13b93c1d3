"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import {
  Users,
  MapPin,
  Building2,
  Megaphone,
  ArrowRight,
  Sparkles,
  TrendingUp,
  Heart,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

interface VillageInfo {
  id: string;
  name: string;
  demographics: any;
}

interface Announcement {
  id: string;
  title: string;
  content: string;
  is_important: boolean;
  created_at: string;
}

export default function Home() {
  const [villageInfo, setVillageInfo] = useState<VillageInfo | null>(null);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch village info
      const { data: village } = await supabase
        .from("village_info")
        .select("*")
        .single();

      // Fetch recent announcements
      const { data: announcementsData } = await supabase
        .from("announcements")
        .select("*")
        .order("created_at", { ascending: false })
        .limit(5);

      setVillageInfo(village);
      setAnnouncements(announcementsData || []);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data...</p>
        </div>
      </div>
    );
  }

  const demographics = villageInfo?.demographics || {};

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-green-50">
      {/* Hero Section */}
      <section className="relative py-24 md:py-32 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/image/bg_1.jpg"
            alt="Background"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/60"></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 animate-bounce">
          <Sparkles className="w-8 h-8 text-yellow-300 opacity-70" />
        </div>
        <div className="absolute top-32 right-16 animate-pulse">
          <Heart className="w-6 h-6 text-red-300 opacity-60" />
        </div>
        <div className="absolute bottom-20 left-20 animate-bounce delay-300">
          <TrendingUp className="w-7 h-7 text-green-300 opacity-70" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="backdrop-blur-sm bg-white/10 rounded-3xl p-8 md:p-12 border border-white/20 shadow-2xl">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              {villageInfo?.name || "Desa Kami"}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto text-gray-100 leading-relaxed">
              Portal informasi resmi desa dengan layanan terpadu untuk
              masyarakat yang modern dan inovatif
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/profil">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-white to-gray-100 text-gray-900 hover:from-gray-100 hover:to-white shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 font-semibold px-8 py-4"
                >
                  Pelajari Lebih Lanjut
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/umkm">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 font-semibold px-8 py-4"
                >
                  Lihat UMKM
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Statistics */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Data Demografis
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Informasi terkini mengenai jumlah penduduk dan komposisi
              masyarakat desa
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-emerald-100 group">
              <CardContent className="p-8">
                <div className="bg-gradient-to-r from-green-500 to-emerald-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-2">
                  {demographics.population || 0}
                </h3>
                <p className="text-gray-600 font-medium">Total Penduduk</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-cyan-100 group">
              <CardContent className="p-8">
                <div className="bg-gradient-to-r from-blue-500 to-cyan-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Building2 className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-2">
                  {demographics.families || 0}
                </h3>
                <p className="text-gray-600 font-medium">Kepala Keluarga</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-violet-100 group">
              <CardContent className="p-8">
                <div className="bg-gradient-to-r from-purple-500 to-violet-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-2">
                  {demographics.males || 0}
                </h3>
                <p className="text-gray-600 font-medium">Laki-laki</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-rose-100 group">
              <CardContent className="p-8">
                <div className="bg-gradient-to-r from-pink-500 to-rose-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-2">
                  {demographics.females || 0}
                </h3>
                <p className="text-gray-600 font-medium">Perempuan</p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Announcements */}
        <section>
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="bg-gradient-to-r from-orange-500 to-red-600 w-12 h-12 rounded-2xl flex items-center justify-center">
                <Megaphone className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-4xl font-bold text-gray-900">
                Pengumuman Terbaru
              </h2>
            </div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Informasi dan berita terkini dari pemerintah desa untuk masyarakat
            </p>
          </div>

          {announcements.length > 0 ? (
            <div className="grid gap-8">
              {announcements.map((announcement, index) => (
                <Card
                  key={announcement.id}
                  className="hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 border-0 bg-gradient-to-r from-white to-gray-50 group overflow-hidden"
                >
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-green-500"></div>
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-3 flex-1">
                        <div className="flex items-center space-x-3">
                          <div className="bg-gradient-to-r from-blue-100 to-green-100 px-3 py-1 rounded-full">
                            <span className="text-sm font-medium text-gray-700">
                              #{index + 1}
                            </span>
                          </div>
                          <CardTitle className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                            {announcement.title}
                          </CardTitle>
                          {announcement.is_important && (
                            <Badge className="bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 shadow-lg animate-pulse">
                              Penting
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-gray-500">
                          <MapPin className="w-4 h-4" />
                          <p className="text-sm font-medium">
                            {new Date(
                              announcement.created_at
                            ).toLocaleDateString("id-ID", {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            })}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {announcement.content}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="border-0 bg-gradient-to-br from-gray-50 to-blue-50">
              <CardContent className="text-center py-16">
                <div className="bg-gradient-to-r from-gray-400 to-blue-500 w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6">
                  <Megaphone className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Belum Ada Pengumuman
                </h3>
                <p className="text-gray-600 text-lg">
                  Pengumuman terbaru akan muncul di sini
                </p>
              </CardContent>
            </Card>
          )}
        </section>
      </div>
    </div>
  );
}
