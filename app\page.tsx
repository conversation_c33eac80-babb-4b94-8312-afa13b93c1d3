"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import { Users, MapPin, Building2, Megaphone, ArrowRight } from "lucide-react";
import Link from "next/link";

interface VillageInfo {
  id: string;
  name: string;
  demographics: any;
}

interface Announcement {
  id: string;
  title: string;
  content: string;
  is_important: boolean;
  created_at: string;
}

export default function Home() {
  const [villageInfo, setVillageInfo] = useState<VillageInfo | null>(null);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch village info
      const { data: village } = await supabase
        .from("village_info")
        .select("*")
        .single();

      // Fetch recent announcements
      const { data: announcementsData } = await supabase
        .from("announcements")
        .select("*")
        .order("created_at", { ascending: false })
        .limit(5);

      setVillageInfo(village);
      setAnnouncements(announcementsData || []);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data...</p>
        </div>
      </div>
    );
  }

  const demographics = villageInfo?.demographics || {};

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Selamat Datang di {villageInfo?.name || "Desa Kami"}
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Portal informasi resmi desa dengan layanan terpadu untuk masyarakat
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/profil">
              <Button
                size="lg"
                className="bg-white text-green-600 hover:bg-gray-100"
              >
                Pelajari Lebih Lanjut
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </Link>
            <Link href="/umkm">
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-green-600"
              >
                Lihat UMKM
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Statistics */}
        <section className="mb-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <Users className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900">
                  {demographics.population || 0}
                </h3>
                <p className="text-gray-600">Total Penduduk</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <Building2 className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900">
                  {demographics.families || 0}
                </h3>
                <p className="text-gray-600">Kepala Keluarga</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <Users className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900">
                  {demographics.males || 0}
                </h3>
                <p className="text-gray-600">Laki-laki</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <Users className="w-12 h-12 text-pink-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900">
                  {demographics.females || 0}
                </h3>
                <p className="text-gray-600">Perempuan</p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Announcements */}
        <section>
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <Megaphone className="w-8 h-8 text-green-600" />
              <h2 className="text-3xl font-bold text-gray-900">
                Pengumuman Terbaru
              </h2>
            </div>
          </div>

          {announcements.length > 0 ? (
            <div className="grid gap-6">
              {announcements.map((announcement) => (
                <Card
                  key={announcement.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-xl">
                            {announcement.title}
                          </CardTitle>
                          {announcement.is_important && (
                            <Badge variant="destructive">Penting</Badge>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm">
                          {new Date(announcement.created_at).toLocaleDateString(
                            "id-ID",
                            {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            }
                          )}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 leading-relaxed">
                      {announcement.content}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Megaphone className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 text-lg">
                  Belum ada pengumuman terbaru
                </p>
              </CardContent>
            </Card>
          )}
        </section>
      </div>
    </div>
  );
}
